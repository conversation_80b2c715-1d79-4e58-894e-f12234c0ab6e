import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import GoogleLoginButton from '../components/GoogleLoginButton';
import {
  Box,
  Container,
  Paper,
  Typography,
  Alert
} from '@mui/material';

function LoginPage() {
  const navigate = useNavigate();
  const { googleLogin } = useAuth();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleGoogleSuccess = async (googleUserData, credential) => {
    setLoading(true);
    setError('');

    try {
      const result = await googleLogin(googleUserData, credential);

      if (result.success) {
        if (result.user.is_admin) {
          navigate('/admin');
        } else {
          navigate('/');
        }
      } else {
        setError(result.error || 'Google 登入失敗');
      }
    } catch (err) {
      setError('Google 登入失敗，請稍後再試');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleError = (error) => {
    console.error('Google 登入錯誤:', error);
    setError('Google 登入發生錯誤，請稍後再試');
  };

  const containerStyle = {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: 'linear-gradient(135deg, #1a237e 0%, #121212 50%, #1a237e 100%)',
    position: 'relative',
    overflow: 'hidden'
  };

  const paperStyle = {
    padding: '3rem',
    background: 'rgba(18, 18, 18, 0.95)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(0, 192, 255, 0.3)',
    borderRadius: '20px',
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
    width: '100%',
    maxWidth: '400px',
    position: 'relative',
    overflow: 'hidden'
  };

  const titleStyle = {
    background: 'linear-gradient(45deg, #00c0ff, #0080ff)',
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '2rem',
    fontSize: '2rem'
  };

  return (
    <Box style={containerStyle}>
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 1 }}>
        <Paper elevation={24} style={paperStyle}>
          <Typography variant="h4" style={titleStyle}>
            歡迎回到 Synctix
          </Typography>

          <Typography
            variant="body1"
            sx={{
              color: '#aaa',
              textAlign: 'center',
              mb: 3,
              fontSize: '1.1rem'
            }}
          >
            使用 Google 帳號快速登入
          </Typography>

          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(244, 67, 54, 0.1)',
                color: '#ff6b6b',
                border: '1px solid rgba(244, 67, 54, 0.3)',
                '& .MuiAlert-icon': {
                  color: '#ff6b6b'
                }
              }}
            >
              {error}
            </Alert>
          )}

          <Box sx={{ mb: 3 }}>
            <GoogleLoginButton
              onSuccess={handleGoogleSuccess}
              onError={handleGoogleError}
              disabled={loading}
            />
          </Box>

          <Typography
            variant="body2"
            sx={{
              color: '#666',
              textAlign: 'center',
              fontSize: '0.9rem'
            }}
          >
            登入即表示您同意我們的服務條款和隱私政策
          </Typography>
        </Paper>
      </Container>
    </Box>
  );
}

export default LoginPage;
