import React, { useEffect, useRef } from 'react';
import { Button, Box } from '@mui/material';
import { Google } from '@mui/icons-material';

const GoogleLoginButton = ({ onSuccess, onError, disabled = false }) => {
  const googleButtonRef = useRef(null);

  useEffect(() => {
    console.log('GoogleLoginButton useEffect 執行');

    // 確保 Google Identity Services 已載入
    if (window.google && window.google.accounts) {
      console.log('Google API 已載入，立即初始化');
      initializeGoogleSignIn();
    } else {
      console.log('Google API 未載入，等待載入...');
      // 如果還沒載入，等待載入完成
      const checkGoogleLoaded = setInterval(() => {
        console.log('檢查 Google API 載入狀態...');
        if (window.google && window.google.accounts) {
          console.log('Google API 載入完成，開始初始化');
          clearInterval(checkGoogleLoaded);
          initializeGoogleSignIn();
        }
      }, 100);

      // 清理定時器
      return () => {
        console.log('清理 Google API 檢查定時器');
        clearInterval(checkGoogleLoaded);
      };
    }
  }, []);

  const initializeGoogleSignIn = () => {
    try {
      const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
      console.log('初始化 Google Sign-In，Client ID:', clientId);

      if (!clientId || clientId === "YOUR_GOOGLE_CLIENT_ID") {
        console.error('Google Client ID 未設定或無效');
        if (onError) {
          onError(new Error('Google Client ID 未設定'));
        }
        return;
      }

      window.google.accounts.id.initialize({
        client_id: clientId,
        callback: handleCredentialResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      console.log('Google Sign-In 初始化成功');

      // 渲染 Google 登入按鈕
      if (googleButtonRef.current) {
        console.log('渲染 Google 按鈕');
        window.google.accounts.id.renderButton(
          googleButtonRef.current,
          {
            theme: "filled_blue",
            size: "large",
            text: "signin_with",
            shape: "rectangular",
            logo_alignment: "left",
            width: "100%"
          }
        );
        console.log('Google 按鈕渲染完成');
      } else {
        console.error('googleButtonRef.current 為 null');
      }
    } catch (error) {
      console.error('Google Sign-In 初始化失敗:', error);
      if (onError) {
        onError(error);
      }
    }
  };

  const handleCredentialResponse = async (response) => {
    try {
      // 解碼 JWT token 來獲取用戶資訊
      const userInfo = parseJwt(response.credential);
      
      // 準備用戶資料
      const userData = {
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        google_id: userInfo.sub,
        email_verified: userInfo.email_verified
      };

      if (onSuccess) {
        onSuccess(userData, response.credential);
      }
    } catch (error) {
      console.error('處理 Google 登入回應失敗:', error);
      if (onError) {
        onError(error);
      }
    }
  };

  // 解析 JWT token
  const parseJwt = (token) => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('解析 JWT token 失敗:', error);
      throw error;
    }
  };

  const handleCustomButtonClick = () => {
    if (window.google && window.google.accounts) {
      try {
        // 使用 prompt 方法觸發 Google 登入
        window.google.accounts.id.prompt((notification) => {
          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            // 如果 prompt 失敗，嘗試點擊隱藏的 Google 按鈕
            if (googleButtonRef.current) {
              const googleBtn = googleButtonRef.current.querySelector('div[role="button"]');
              if (googleBtn) {
                googleBtn.click();
              }
            }
          }
        });
      } catch (error) {
        if (onError) {
          onError(error);
        }
      }
    } else {
      if (onError) {
        onError(new Error('Google API 未載入'));
      }
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* 隱藏的 Google 按鈕容器 */}
      <div
        ref={googleButtonRef}
        style={{
          display: 'none'
        }}
      />

      {/* 自定義樣式的 Google 登入按鈕 */}
      <Button
        fullWidth
        variant="contained"
        disabled={disabled}
        onClick={handleCustomButtonClick}
        startIcon={<Google />}
        sx={{
          backgroundColor: '#4285f4',
          color: 'white',
          fontWeight: 'bold',
          padding: '12px 0',
          clipPath: 'polygon(10% 0, 100% 0, 90% 100%, 0% 100%)',
          '&:hover': {
            backgroundColor: '#357ae8',
            boxShadow: '0 0 20px rgba(66, 133, 244, 0.5)'
          },
          '&:disabled': {
            backgroundColor: '#555',
            color: '#888'
          }
        }}
      >
        使用 Google 帳號登入
      </Button>
    </Box>
  );
};

export default GoogleLoginButton;
