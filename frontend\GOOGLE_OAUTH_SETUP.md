# Google OAuth 設置指南

## 🔧 設置步驟

### 1. 創建 Google Cloud 專案

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 創建新專案或選擇現有專案
3. 啟用 Google+ API 和 Google Identity Services

### 2. 設置 OAuth 2.0 憑證

1. 在 Google Cloud Console 中，前往「API 和服務」→「憑證」
2. 點擊「建立憑證」→「OAuth 2.0 用戶端 ID」
3. 選擇應用程式類型：「網頁應用程式」
4. 設置授權的 JavaScript 來源：
   - `http://localhost:5173` (開發環境)
   - 您的生產環境網域
5. 設置授權的重新導向 URI：
   - `http://localhost:5173` (開發環境)
   - 您的生產環境網域

### 3. 配置環境變數

1. 複製您的 Client ID
2. 在 `frontend/.env` 文件中設置：
   ```
   VITE_GOOGLE_CLIENT_ID=您的_CLIENT_ID_這裡
   ```

### 4. 測試 Google 登入

1. 啟動前端服務：
   ```bash
   cd frontend
   npm run dev
   ```

2. 啟動後端服務：
   ```bash
   cd pybackend
   uv run python src/main.py
   ```

3. 前往 http://localhost:5173/login
4. 點擊「使用 Google 帳號登入」按鈕

## 🔒 安全注意事項

- 不要將 Client ID 提交到公開的版本控制系統
- 在生產環境中使用 HTTPS
- 定期檢查和更新 OAuth 設置
- 限制授權來源網域

## 🐛 常見問題

### Q: 點擊 Google 登入按鈕沒有反應
A: 檢查：
1. Google Client ID 是否正確設置
2. 網域是否在 Google Console 中授權
3. 瀏覽器控制台是否有錯誤訊息

### Q: 登入後出現錯誤
A: 檢查：
1. 後端 API 是否正常運行
2. 網路連接是否正常
3. 後端日誌中的錯誤訊息

## 📝 功能說明

- **自動註冊**: 首次使用 Google 登入的用戶會自動創建帳號
- **資料同步**: Google 頭像和姓名會自動同步
- **安全性**: 使用 Google 的 OAuth 2.0 標準
- **用戶體驗**: 無需記住額外密碼
