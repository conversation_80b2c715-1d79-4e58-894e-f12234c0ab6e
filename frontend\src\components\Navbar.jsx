import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AppBar, Toolbar, Typography, Button, Box, Menu, MenuItem, Avatar } from '@mui/material';
import { Person, ExitToApp, Dashboard } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import ThemeToggle from './ThemeToggle';

const Navbar = () => {
  const { user, logout, isAuthenticated, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState(null);

  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
    navigate('/');
  };
  const navStyle = {
    backgroundColor: 'rgba(18, 18, 18, 0.85)', // 半透明的深色背景
    backdropFilter: 'blur(10px)', // 毛玻璃效果
    borderBottom: '1px solid #00c0ff',
    clipPath: 'polygon(0 0, 100% 0, 100% 100%, 5% 100%, 0 80%)', // 不對稱的切割造型
  };

  const logoStyle = {
    flexGrow: 1,
    fontWeight: 'bold',
    color: '#00c0ff', // 科技藍色
    textShadow: '0 0 5px #00c0ff',
    fontFamily: 'Exo 2, sans-serif', // 已修正的字體名稱
  };

  const buttonStyle = {
    color: 'white',
    fontFamily: 'Exo 2, sans-serif', // 已修正的字體名稱
    position: 'relative',
    padding: '8px 16px',
    border: '1px solid transparent',
    '&:before': {
        content: '""' , // 確保 content 屬性被正確引用
        position: 'absolute',
        top: 0,
        left: 0,
        width: '0',
        height: '100%',
        borderBottom: '2px solid #00c0ff',
        transition: 'width 0.3s ease-out',
    },
    '&:hover:before': {
        width: '100%',
    },
    '&:hover': {
        backgroundColor: 'rgba(0, 192, 255, 0.1)',
    }
  };

  return (
    <AppBar position="sticky" style={navStyle}>
      <Toolbar>
        <Box component={Link} to="/" sx={{
          ...logoStyle,
          textDecoration: 'none',
          display: 'flex',
          alignItems: 'center',
          gap: 1.5
        }}>
          <img
            src="/icon/icon.png"
            alt="Synctix Logo"
            style={{
              width: '32px',
              height: '32px',
              filter: 'drop-shadow(0 0 8px #00c0ff)'
            }}
          />
          <Typography variant="h6" component="span" sx={{
            fontWeight: 'bold',
            color: '#00c0ff',
            textShadow: '0 0 5px #00c0ff',
            fontFamily: 'Exo 2, sans-serif'
          }}>
            Synctix
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button component={Link} to="/" sx={buttonStyle}>
            探索活動
          </Button>

          {isAuthenticated() ? (
            <>
              {isAdmin() && (
                <Button component={Link} to="/admin" sx={buttonStyle}>
                  <Dashboard sx={{ mr: 0.5, fontSize: '1rem' }} />
                  管理後台
                </Button>
              )}
              <Button
                onClick={handleUserMenuOpen}
                sx={{
                  ...buttonStyle,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <Avatar
                  sx={{
                    width: 24,
                    height: 24,
                    backgroundColor: '#00c0ff',
                    fontSize: '0.8rem'
                  }}
                >
                  {user?.name?.charAt(0) || 'U'}
                </Avatar>
                {user?.name || '用戶'}
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleUserMenuClose}
                PaperProps={{
                  sx: {
                    backgroundColor: 'rgba(36, 36, 36, 0.95)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid #444',
                    borderRadius: '0',
                    mt: 1
                  }
                }}
              >
                <MenuItem
                  onClick={() => {
                    handleUserMenuClose();
                    navigate('/profile');
                  }}
                  sx={{
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 192, 255, 0.1)'
                    }
                  }}
                >
                  <Person sx={{ mr: 1, color: '#00c0ff' }} />
                  個人資料
                </MenuItem>
                <MenuItem
                  onClick={handleLogout}
                  sx={{
                    color: 'white',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 107, 0.1)'
                    }
                  }}
                >
                  <ExitToApp sx={{ mr: 1, color: '#ff6b6b' }} />
                  登出
                </MenuItem>
              </Menu>
            </>
          ) : (
            <Button component={Link} to="/login" sx={buttonStyle}>
              登入
            </Button>
          )}

          <ThemeToggle />
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
