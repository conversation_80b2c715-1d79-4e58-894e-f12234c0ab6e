import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Container,
  Paper,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Divider,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert
} from '@mui/material';
import {
  Person,
  Email,
  Phone,
  ConfirmationNumber,
  QrCode,
  CalendarToday,
  LocationOn,
  AttachMoney,
  ExitToApp
} from '@mui/icons-material';

function ProfilePage() {
  const navigate = useNavigate();
  const { user: authUser, logout, isAuthenticated } = useAuth();
  const [user, setUser] = useState(null);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    console.log('ProfilePage useEffect - authUser:', authUser);
    console.log('ProfilePage useEffect - isAuthenticated:', isAuthenticated());

    if (!isAuthenticated()) {
      console.log('用戶未認證，重定向到登入頁面');
      navigate('/login');
      return;
    }

    if (!authUser) {
      console.log('authUser 為空，等待用戶數據載入');
      return;
    }

    const fetchUserData = async () => {
      try {
        setLoading(true);
        console.log('開始獲取用戶資料，用戶 ID:', authUser.id);

        // 獲取用戶詳細資料
        const userResponse = await fetch(`http://localhost:8000/api/users/${authUser.id}`);
        console.log('用戶資料 API 回應狀態:', userResponse.status);

        if (userResponse.ok) {
          const userData = await userResponse.json();
          console.log('獲取到的用戶資料:', userData);
          setUser(userData);
        } else {
          console.log('用戶資料 API 失敗，使用 authUser 資料');
          setUser(authUser);
        }

        // 獲取用戶訂單
        const ordersResponse = await fetch(`http://localhost:8000/api/users/${authUser.id}/orders`);
        console.log('訂單 API 回應狀態:', ordersResponse.status);

        if (ordersResponse.ok) {
          const ordersData = await ordersResponse.json();
          console.log('獲取到的訂單資料:', ordersData);
          setOrders(ordersData.orders || []);
        } else {
          console.log('訂單 API 失敗，設為空陣列');
          setOrders([]);
        }

      } catch (error) {
        console.error('獲取用戶資料錯誤:', error);
        // 如果 API 失敗，使用 authUser 的基本資料
        setUser(authUser);
        setOrders([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [authUser, isAuthenticated, navigate]);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return '#4caf50';
      case 'pending':
        return '#ff9800';
      case 'cancelled':
        return '#f44336';
      default:
        return '#666';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return '已付款';
      case 'pending':
        return '待付款';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Typography>載入中...</Typography>
      </Box>
    );
  }

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="warning">
          請先登入以查看個人資料
          <Button onClick={() => navigate('/login')} sx={{ ml: 2 }}>
            前往登入
          </Button>
        </Alert>
      </Container>
    );
  }

  const containerStyle = {
    minHeight: '100vh',
    backgroundColor: '#121212',
    color: 'white',
    paddingY: '2rem'
  };

  const paperStyle = {
    backgroundColor: 'rgba(36, 36, 36, 0.95)',
    backdropFilter: 'blur(10px)',
    border: '1px solid #444',
    borderRadius: '0',
    clipPath: 'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px))',
    padding: '2rem',
    marginBottom: '2rem'
  };

  return (
    <Box sx={containerStyle}>
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* 左側：個人資料 */}
          <Grid item xs={12} md={4}>
            <Paper elevation={0} sx={paperStyle}>
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    backgroundColor: '#00c0ff',
                    fontSize: '2rem',
                    mx: 'auto',
                    mb: 2
                  }}
                >
                  {user.name.charAt(0)}
                </Avatar>
                <Typography variant="h5" sx={{ color: 'white', fontWeight: 'bold', mb: 1 }}>
                  {user.name}
                </Typography>
                <Typography variant="body2" sx={{ color: '#aaa' }}>
                  會員編號：{user.id}
                </Typography>
              </Box>

              <Divider sx={{ backgroundColor: '#444', my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Email sx={{ color: '#00c0ff', mr: 1, fontSize: '1.2rem' }} />
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    電子郵件
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ color: 'white', ml: 3 }}>
                  {user.email}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Phone sx={{ color: '#00c0ff', mr: 1, fontSize: '1.2rem' }} />
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    手機號碼
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ color: 'white', ml: 3 }}>
                  {user.phone}
                </Typography>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarToday sx={{ color: '#00c0ff', mr: 1, fontSize: '1.2rem' }} />
                  <Typography variant="body2" sx={{ color: '#aaa' }}>
                    註冊時間
                  </Typography>
                </Box>
                <Typography variant="body1" sx={{ color: 'white', ml: 3 }}>
                  {new Date(user.created_at).toLocaleDateString('zh-TW')}
                </Typography>
              </Box>

              <Button
                variant="outlined"
                fullWidth
                onClick={handleLogout}
                sx={{
                  color: '#ff6b6b',
                  borderColor: '#ff6b6b',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderColor: '#ff6b6b'
                  }
                }}
              >
                <ExitToApp sx={{ mr: 1 }} />
                登出
              </Button>
            </Paper>
          </Grid>

          {/* 右側：購票記錄 */}
          <Grid item xs={12} md={8}>
            <Paper elevation={0} sx={paperStyle}>
              <Typography variant="h5" sx={{ color: '#00c0ff', fontWeight: 'bold', mb: 3 }}>
                <ConfirmationNumber sx={{ mr: 1, verticalAlign: 'middle' }} />
                購票記錄
              </Typography>

              {orders.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="h6" sx={{ color: '#666', mb: 2 }}>
                    尚無購票記錄
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/')}
                    sx={{
                      backgroundColor: '#00c0ff',
                      color: 'black',
                      '&:hover': {
                        backgroundColor: '#64d8ff'
                      }
                    }}
                  >
                    立即購票
                  </Button>
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          演唱會
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          票種/數量
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          金額
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          狀態
                        </TableCell>
                        <TableCell sx={{ color: '#00c0ff', fontWeight: 'bold', borderColor: '#444' }}>
                          操作
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {orders.map((order) => (
                        <TableRow key={order.id}>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                              {order.concert_name}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#aaa' }}>
                              {new Date(order.created_at).toLocaleDateString('zh-TW')}
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            <Typography variant="body2">
                              {order.items[0].ticket_type_name}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#aaa' }}>
                              {order.items[0].quantity} 張
                            </Typography>
                          </TableCell>
                          <TableCell sx={{ color: 'white', borderColor: '#444' }}>
                            NT$ {order.total_amount.toLocaleString()}
                          </TableCell>
                          <TableCell sx={{ borderColor: '#444' }}>
                            <Chip
                              label={getStatusText(order.status)}
                              size="small"
                              sx={{
                                backgroundColor: getStatusColor(order.status),
                                color: 'white',
                                fontWeight: 'bold'
                              }}
                            />
                          </TableCell>
                          <TableCell sx={{ borderColor: '#444' }}>
                            {order.status === 'paid' && order.qr_code && (
                              <Button
                                size="small"
                                variant="outlined"
                                href={order.qr_code}
                                target="_blank"
                                sx={{
                                  color: '#00c0ff',
                                  borderColor: '#00c0ff',
                                  minWidth: 'auto',
                                  '&:hover': {
                                    backgroundColor: 'rgba(0, 192, 255, 0.1)'
                                  }
                                }}
                              >
                                <QrCode sx={{ fontSize: '1rem' }} />
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Paper>

            {/* 統計資訊 */}
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Card sx={{
                  backgroundColor: 'rgba(0, 192, 255, 0.1)',
                  border: '1px solid #00c0ff',
                  borderRadius: '0'
                }}>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ color: '#00c0ff', fontWeight: 'bold' }}>
                      {orders.length}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#aaa' }}>
                      總購票次數
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Card sx={{
                  backgroundColor: 'rgba(76, 175, 80, 0.1)',
                  border: '1px solid #4caf50',
                  borderRadius: '0'
                }}>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ color: '#4caf50', fontWeight: 'bold' }}>
                      {orders.reduce((sum, order) => sum + order.items[0].quantity, 0)}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#aaa' }}>
                      總票券數量
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Card sx={{
                  backgroundColor: 'rgba(255, 193, 7, 0.1)',
                  border: '1px solid #ffc107',
                  borderRadius: '0'
                }}>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ color: '#ffc107', fontWeight: 'bold' }}>
                      {orders.reduce((sum, order) => sum + order.total_amount, 0).toLocaleString()}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#aaa' }}>
                      總消費金額 (NT$)
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default ProfilePage;
