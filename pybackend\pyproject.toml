[project]
name = "synctix-backend"
version = "1.0.0"
description = "Synctix 票務平台整合後端服務"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # 原 pybackend 依賴
    "fastapi>=0.116.1",
    "google-auth>=2.40.3",
    "jose>=1.0.0",
    "jupyterlab>=4.4.4",
    "passlib[bcrypt]>=1.7.4",
    "pydantic[email]>=2.11.7",
    "python-dotenv>=1.1.1",
    "python-jose[cryptography]>=3.5.0",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.41",
    "types-passlib>=1.7.7.20250602",
    "types-python-jose>=3.5.0.20250531",
    "uvicorn>=0.35.0",

    # 從 frontend/backend 合併的依賴
    "aiofiles>=24.1.0",
    "aiohttp>=3.12.14",
    "pillow>=11.3.0",
    "requests>=2.32.4",
]
