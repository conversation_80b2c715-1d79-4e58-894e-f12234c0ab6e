from typing import List, Optional

from pydantic import BaseModel, EmailStr


# --- User Schemas ---
# 用於註冊時接收的資料
class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str  # 前端傳來的是明文密碼


# 用於 API 回應時，保護敏感資訊 (如密碼)
class UserResponse(BaseModel):
    id: int
    username: str
    email: EmailStr
    is_active: bool

    class Config:
        orm_mode = True  # 讓 Pydantic 能從 SQLAlchemy 物件讀取資料


# --- Token Schemas ---
# 用於登入成功後的回應
class Token(BaseModel):
    access_token: str
    token_type: str


# 用於解析 JWT 權杖內容
class TokenData(BaseModel):
    username: Optional[str] = None


# --- Google Token Schema ---
class GoogleToken(BaseModel):
    credential: str


# --- 票務系統相關 Schemas ---


# 票種相關模型
class TicketType(BaseModel):
    id: int
    name: str
    price: int
    available: int
    total: int
    description: Optional[str] = None


class TicketTypeCreate(BaseModel):
    name: str
    price: int
    total: int
    description: str


# 演唱會相關模型
class Concert(BaseModel):
    id: int
    name: str
    artist: str
    date: str
    time: str
    location: str
    address: str
    poster_url: str
    description: str
    categories: List[str]
    ticketSaleStart: str
    ticketTypes: List[TicketType]
    importantNotes: List[str]
    purchaseInstructions: List[str]
    legalNotices: List[str]
    status: str  # "upcoming", "on_sale", "sold_out", "ended"
    featured: bool = False


class ConcertCreate(BaseModel):
    name: str
    artist: str
    date: str
    time: str
    location: str
    address: str
    poster_url: str
    description: str
    categories: List[str]
    ticketSaleStart: str
    ticketTypes: List[TicketTypeCreate]
    importantNotes: List[str]
    purchaseInstructions: List[str]
    legalNotices: List[str]
    status: str
    featured: bool = False


# 用戶相關模型（票務系統用）
class SynctixUser(BaseModel):
    id: str
    email: str
    name: str
    phone: str
    created_at: str
    is_admin: bool = False


class UserRegistration(BaseModel):
    email: str
    password: str
    name: str
    phone: str


class UserLogin(BaseModel):
    email: str
    password: str


class GoogleLoginData(BaseModel):
    email: str
    name: str
    picture: Optional[str] = None
    google_id: str
    credential: str


# 訂單相關模型
class OrderItem(BaseModel):
    ticket_type_id: int
    ticket_type_name: str
    price: int
    quantity: int


class CustomerInfo(BaseModel):
    name: str
    email: str
    phone: str


class Order(BaseModel):
    id: str
    user_id: str
    concert_id: int
    concert_name: str
    customer_info: CustomerInfo
    items: List[OrderItem]
    total_amount: int
    status: str  # "pending", "paid", "cancelled", "refunded"
    payment_method: str
    created_at: str
    paid_at: Optional[str] = None
    qr_code: Optional[str] = None


class CreateOrderRequest(BaseModel):
    concert_id: int
    ticket_type_id: int
    quantity: int
    customer_info: CustomerInfo
    user_id: Optional[str] = None


# 付款相關模型
class PaymentInfo(BaseModel):
    payment_method: str  # "credit_card", "bank_transfer", "cash"
